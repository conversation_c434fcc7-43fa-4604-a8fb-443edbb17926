import { useRouter, useLocalSearchParams } from "expo-router";
import { useState } from "react";
import ScreenHeader from "src/components/ScreenHeader";
import { Button, Text, View, YStack } from "tamagui";
import { useTelehealthConsentStyle } from "./Styles/TelehealthConsentStyle";
import SheetDemo from "src/components/SettingsDrawer";
import { CallPendingDialog } from "src/components/CallPendingDialog";
import { ScrollView } from "react-native";

export default function TelehealthConsent() {
  const {
    facilityId,
    patientId,
    reason,
    patientFirstName,
    patientDOB,
    fileKeys,
    fileNames,
    fileTypes,
  } = useLocalSearchParams();
  const telehealthConsentStyle = useTelehealthConsentStyle();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const openSettings = () => setOpen(true);
  const navigateBack = () => router.back();
  const [sendingRequest, setSendingRequest] = useState(false);
  let parsedFileKeys: string[] = [];
  let parsedFileNames: string[] = [];
  let parsedFileTypes: string[] = [];
  try {
    parsedFileKeys = fileKeys ? JSON.parse(fileKeys as string) : [];
  } catch {
    parsedFileKeys = [];
  }
  try {
    parsedFileNames = fileNames ? JSON.parse(fileNames as string) : [];
  } catch {
    parsedFileNames = [];
  }
  try {
    parsedFileTypes = fileTypes ? JSON.parse(fileTypes as string) : [];
  } catch {
    parsedFileTypes = [];
  }

  function getStringParam(param: string | string[] | undefined): string {
    if (Array.isArray(param)) return param[0] || "";
    return param || "";
  }

  const safeFacilityId = getStringParam(facilityId);
  const safePatientId = getStringParam(patientId);
  const safeReason = getStringParam(reason);
  const safePatientFirstName = getStringParam(patientFirstName);
  const safePatientDOB = getStringParam(patientDOB);

  return (
    <View {...telehealthConsentStyle.container}>
      <YStack {...telehealthConsentStyle.mainStack}>
        <ScreenHeader
          onAvatarPress={openSettings}
          screenName="Back"
          onBackPress={navigateBack}
        />
        <YStack {...telehealthConsentStyle.headerTextContainer}>
          <Text {...telehealthConsentStyle.headerText}>
            Telehealth Informed Consent
          </Text>
        </YStack>
        <YStack {...telehealthConsentStyle.consentContainer}>
          <ScrollView showsVerticalScrollIndicator={false}>
          <TelehealthConsent/>
          </ScrollView>
        </YStack>
      </YStack>

      <View {...telehealthConsentStyle.agreeAndConnectBtnContainer}>
        <Button
          {...telehealthConsentStyle.agreeAndConnectBtn}
          onPress={() => setSendingRequest(true)}
        >
          Agree & Connect
        </Button>
        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </View>
      {sendingRequest && (
        <CallPendingDialog
          open={sendingRequest}
          onClose={setSendingRequest}
          requestData={{
            facilityId: safeFacilityId,
            patientId: safePatientId,
            reason: safeReason,
            patientFirstName: safePatientFirstName,
            patientDOB: safePatientDOB,
            fileKeys: parsedFileKeys,
            fileNames: parsedFileNames,
            fileTypes: parsedFileTypes,
          }}
        />
      )}
    </View>
  );
}
